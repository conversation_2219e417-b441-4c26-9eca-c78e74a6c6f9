#!/usr/bin/env python3
"""
Simple script to create a basic favicon.ico file
"""

def create_simple_favicon():
    # This is a minimal 16x16 ICO file with a simple pattern
    # ICO file format header + 16x16 bitmap data
    ico_data = bytes([
        # ICO header (6 bytes)
        0x00, 0x00,  # Reserved (must be 0)
        0x01, 0x00,  # Type (1 = ICO)
        0x01, 0x00,  # Number of images
        
        # Image directory entry (16 bytes)
        0x10,        # Width (16 pixels)
        0x10,        # Height (16 pixels)
        0x00,        # Color count (0 = no palette)
        0x00,        # Reserved
        0x01, 0x00,  # Color planes
        0x20, 0x00,  # Bits per pixel (32-bit)
        0x00, 0x04, 0x00, 0x00,  # Size of bitmap data (1024 bytes)
        0x16, 0x00, 0x00, 0x00,  # Offset to bitmap data
        
        # Bitmap data (simplified 16x16 32-bit RGBA)
        # This creates a simple geometric pattern
    ])
    
    # Add bitmap header (40 bytes)
    bitmap_header = bytes([
        0x28, 0x00, 0x00, 0x00,  # Header size (40)
        0x10, 0x00, 0x00, 0x00,  # Width (16)
        0x20, 0x00, 0x00, 0x00,  # Height (32, includes AND mask)
        0x01, 0x00,              # Planes (1)
        0x20, 0x00,              # Bits per pixel (32)
        0x00, 0x00, 0x00, 0x00,  # Compression (0 = none)
        0x00, 0x04, 0x00, 0x00,  # Image size (1024)
        0x00, 0x00, 0x00, 0x00,  # X pixels per meter
        0x00, 0x00, 0x00, 0x00,  # Y pixels per meter
        0x00, 0x00, 0x00, 0x00,  # Colors used
        0x00, 0x00, 0x00, 0x00,  # Important colors
    ])
    
    # Create a simple pattern: blue square with white border
    pixel_data = []
    for y in range(16):
        for x in range(16):
            if x == 0 or x == 15 or y == 0 or y == 15:
                # White border
                pixel_data.extend([0xFF, 0xFF, 0xFF, 0xFF])  # BGRA
            elif 4 <= x <= 11 and 4 <= y <= 11:
                # Blue center
                pixel_data.extend([0xFF, 0x00, 0x00, 0xFF])  # BGRA
            else:
                # Light gray
                pixel_data.extend([0xC0, 0xC0, 0xC0, 0xFF])  # BGRA
    
    # Reverse row order (BMP format requirement)
    reversed_pixels = []
    for y in range(15, -1, -1):
        for x in range(16):
            idx = (y * 16 + x) * 4
            reversed_pixels.extend(pixel_data[idx:idx+4])
    
    # AND mask (1 bit per pixel, all transparent)
    and_mask = [0x00] * 32  # 16x16 bits = 32 bytes
    
    # Combine all parts
    full_data = ico_data + bitmap_header + bytes(reversed_pixels) + bytes(and_mask)
    
    with open('funky-snake-toons-dash-main/public/favicon.ico', 'wb') as f:
        f.write(full_data)
    
    print("Created new favicon.ico")

if __name__ == "__main__":
    create_simple_favicon()
